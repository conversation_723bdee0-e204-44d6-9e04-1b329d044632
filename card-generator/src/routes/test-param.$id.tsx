import { createFileRoute, useParams } from "@tanstack/react-router";

function TestParamComponent() {
  const { id } = useParams({ from: "/test-param/$id" });
  
  console.log("TestParamComponent loaded");
  console.log("ID from params:", id);
  
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Param Route</h1>
      <p>This tests parameterized routing.</p>
      <div className="mt-4 p-4 bg-blue-100 rounded">
        <p><strong>ID Parameter:</strong> {id || "No ID found"}</p>
        <p><strong>URL:</strong> {window.location.href}</p>
      </div>
      <div className="mt-4">
        <a href="/custom" className="text-blue-600 hover:underline">
          Back to Custom Types
        </a>
      </div>
    </div>
  );
}

export const Route = createFileRoute("/test-param/$id")({
  component: TestParamComponent,
  errorComponent: ({ error }) => (
    <div className="p-8">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Param Route Error</h1>
      <p className="text-red-500">Error: {error.message}</p>
    </div>
  ),
});
