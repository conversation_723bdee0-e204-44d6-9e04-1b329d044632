import { createFileRoute } from "@tanstack/react-router";

function TestCustomComponent() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Custom Route</h1>
      <p>This is a simple test route to verify routing works.</p>
      <div className="mt-4 p-4 bg-green-100 rounded">
        <p>If you see this, basic routing is working!</p>
      </div>
      <div className="mt-4">
        <a href="/custom" className="text-blue-600 hover:underline">
          Back to Custom Types
        </a>
      </div>
    </div>
  );
}

export const Route = createFileRoute("/test-custom")({
  component: TestCustomComponent,
});
