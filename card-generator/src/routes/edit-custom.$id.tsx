import { createFileRoute, useParams } from "@tanstack/react-router";
import { CustomCardTypeDesigner } from "../components/CustomCardTypeDesigner";

function EditCustomComponent() {
  const { id } = useParams({ from: "/edit-custom/$id" });
  
  console.log("EditCustomComponent loaded");
  console.log("ID from params:", id);
  console.log("Current URL:", window.location.href);
  
  return (
    <div>
      <div className="p-4 bg-yellow-100 border-b">
        <p className="text-sm">
          <strong>Debug:</strong> Route loaded with ID: {id}
        </p>
      </div>
      <CustomCardTypeDesigner />
    </div>
  );
}

export const Route = createFileRoute("/edit-custom/$id")({
  component: EditCustomComponent,
  errorComponent: ({ error }) => (
    <div className="p-8">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Edit Custom Route Error</h1>
      <p className="text-red-500">Error: {error.message}</p>
      <pre className="mt-4 p-4 bg-red-50 rounded text-sm overflow-auto">
        {error.stack}
      </pre>
    </div>
  ),
});
