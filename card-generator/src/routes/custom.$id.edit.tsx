import { createFileRoute, useParams } from "@tanstack/react-router";
import { CustomCardTypeDesigner } from "../components/CustomCardTypeDesigner";

function TestRouteComponent() {
  const { id } = useParams({ from: "/custom/$id/edit" });

  console.log("Route component loaded!");
  console.log("ID from route params:", id);
  console.log("Current URL:", window.location.href);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Route Test - ID: {id}</h1>
      <p>If you see this, the route is working!</p>
      <div className="mt-4 p-4 bg-gray-100 rounded">
        <p>
          <strong>ID:</strong> {id}
        </p>
        <p>
          <strong>URL:</strong> {window.location.href}
        </p>
      </div>
      <div className="mt-8">
        <CustomCardTypeDesigner />
      </div>
    </div>
  );
}

export const Route = createFileRoute("/custom/$id/edit")({
  component: TestRouteComponent,
  errorComponent: ({ error }) => (
    <div className="p-8">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Route Error</h1>
      <p className="text-red-500">Error: {error.message}</p>
      <pre className="mt-4 p-4 bg-red-50 rounded text-sm overflow-auto">
        {error.stack}
      </pre>
    </div>
  ),
});
