/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TestRouteRouteImport } from './routes/test-route'
import { Route as TestCustomRouteImport } from './routes/test-custom'
import { Route as EditorRouteImport } from './routes/editor'
import { Route as DecksRouteImport } from './routes/decks'
import { Route as DeckRouteImport } from './routes/deck'
import { Route as CustomRouteImport } from './routes/custom'
import { Route as IndexRouteImport } from './routes/index'
import { Route as TestParamIdRouteImport } from './routes/test-param.$id'
import { Route as CustomsIdEditRouteImport } from ./routes/customs.$id.editit'

const TestRouteRoute = TestRouteRouteImport.update({
  id: '/test-route',
  path: '/test-route',
  getParentRoute: () => rootRouteImport,
} as any)
const TestCustomRoute = TestCustomRouteImport.update({
  id: '/test-custom',
  path: '/test-custom',
  getParentRoute: () => rootRouteImport,
} as any)
const EditorRoute = EditorRouteImport.update({
  id: '/editor',
  path: '/editor',
  getParentRoute: () => rootRouteImport,
} as any)
const DecksRoute = DecksRouteImport.update({
  id: '/decks',
  path: '/decks',
  getParentRoute: () => rootRouteImport,
} as any)
const DeckRoute = DeckRouteImport.update({
  id: '/deck',
  path: '/deck',
  getParentRoute: () => rootRouteImport,
} as any)
const CustomRoute = CustomRouteImport.update({
  id: '/custom',
  path: '/custom',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const TestParamIdRoute = TestParamIdRouteImport.update({
  id: '/test-param/$id',
  path: '/test-param/$id',
  getParentRoute: () => rootRouteImport,
} as any)
const CustomsIdEditRoute = CustomsIdEditRouteImport.update({
  id: '/customs/$id/edit',
  path: '/customs/$id/edit',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/custom': typeof CustomRoute
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
  '/test-custom': typeof TestCustomRoute
  '/test-route': typeof TestRouteRoute
  '/test-param/$id': typeof TestParamIdRoute
  '/customs/$id/edit': typeof CustomsIdEditRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/custom': typeof CustomRoute
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
  '/test-custom': typeof TestCustomRoute
  '/test-route': typeof TestRouteRoute
  '/test-param/$id': typeof TestParamIdRoute
  '/customs/$id/edit': typeof CustomsIdEditRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/custom': typeof CustomRoute
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
  '/test-custom': typeof TestCustomRoute
  '/test-route': typeof TestRouteRoute
  '/test-param/$id': typeof TestParamIdRoute
  '/customs/$id/edit': typeof CustomsIdEditRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/custom'
    | '/deck'
    | '/decks'
    | '/editor'
    | '/test-custom'
    | '/test-route'
    | '/test-param/$id'
    | '/customs/$id/edit'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/custom'
    | '/deck'
    | '/decks'
    | '/editor'
    | '/test-custom'
    | '/test-route'
    | '/test-param/$id'
    | '/customs/$id/edit'
  id:
    | '__root__'
    | '/'
    | '/custom'
    | '/deck'
    | '/decks'
    | '/editor'
    | '/test-custom'
    | '/test-route'
    | '/test-param/$id'
    | '/customs/$id/edit'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  CustomRoute: typeof CustomRoute
  DeckRoute: typeof DeckRoute
  DecksRoute: typeof DecksRoute
  EditorRoute: typeof EditorRoute
  TestCustomRoute: typeof TestCustomRoute
  TestRouteRoute: typeof TestRouteRoute
  TestParamIdRoute: typeof TestParamIdRoute
  CustomsIdEditRoute: typeof CustomsIdEditRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/test-route': {
      id: '/test-route'
      path: '/test-route'
      fullPath: '/test-route'
      preLoaderRoute: typeof TestRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-custom': {
      id: '/test-custom'
      path: '/test-custom'
      fullPath: '/test-custom'
      preLoaderRoute: typeof TestCustomRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/editor': {
      id: '/editor'
      path: '/editor'
      fullPath: '/editor'
      preLoaderRoute: typeof EditorRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/decks': {
      id: '/decks'
      path: '/decks'
      fullPath: '/decks'
      preLoaderRoute: typeof DecksRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/deck': {
      id: '/deck'
      path: '/deck'
      fullPath: '/deck'
      preLoaderRoute: typeof DeckRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/custom': {
      id: '/custom'
      path: '/custom'
      fullPath: '/custom'
      preLoaderRoute: typeof CustomRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-param/$id': {
      id: '/test-param/$id'
      path: '/test-param/$id'
      fullPath: '/test-param/$id'
      preLoaderRoute: typeof TestParamIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/customs/$id/edit': {
      id: '/customs/$id/edit'
      path: '/customs/$id/edit'
      fullPath: '/customs/$id/edit'
      preLoaderRoute: typeof CustomsIdEditRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  CustomRoute: CustomRoute,
  DeckRoute: DeckRoute,
  DecksRoute: DecksRoute,
  EditorRoute: EditorRoute,
  TestCustomRoute: TestCustomRoute,
  TestRouteRoute: TestRouteRoute,
  TestParamIdRoute: TestParamIdRoute,
  CustomsIdEditRoute: CustomsIdEditRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
