import { useState, useEffect } from "react";
import { useNavigate } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Edit, Trash2, Copy, Palette } from "lucide-react";
import type { CustomCardType } from "../types/customCard";
import { DEFAULT_CUSTOM_CARD_TYPE } from "../types/customCard";
import { CustomCardTypeDesigner } from "./CustomCardTypeDesigner";

interface CustomCardTypeManagerProps {
  onCreateCard?: (customCardTypeId: string) => void;
}

export function CustomCardTypeManager({
  onCreateCard,
}: CustomCardTypeManagerProps) {
  const navigate = useNavigate();
  const [customCardTypes, setCustomCardTypes] = useState<CustomCardType[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newCardType, setNewCardType] = useState({
    name: "",
    description: "",
    width: 63,
    height: 88,
    bleed: 3,
    dpi: 300,
  });

  // Load custom card types from localStorage (temporary storage)
  useEffect(() => {
    const stored = localStorage.getItem("customCardTypes");
    if (stored) {
      try {
        setCustomCardTypes(JSON.parse(stored));
      } catch (error) {
        console.error("Failed to load custom card types:", error);
      }
    }
  }, []);

  // Save custom card types to localStorage
  const saveCustomCardTypes = (types: CustomCardType[]) => {
    localStorage.setItem("customCardTypes", JSON.stringify(types));
    setCustomCardTypes(types);
  };

  const handleCreateNew = () => {
    setNewCardType({
      name: "",
      description: "",
      width: 63,
      height: 88,
      bleed: 3,
      dpi: 300,
    });
    setIsCreateDialogOpen(true);
  };

  const handleCreateCardType = () => {
    if (!newCardType.name.trim()) {
      alert("Please enter a card type name");
      return;
    }

    const cardType: CustomCardType = {
      ...DEFAULT_CUSTOM_CARD_TYPE,
      id: `custom_${Date.now()}`,
      name: newCardType.name,
      description: newCardType.description,
      dimensions: {
        width: newCardType.width,
        height: newCardType.height,
        bleed: newCardType.bleed,
        dpi: newCardType.dpi,
      },
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const updatedTypes = [...customCardTypes, cardType];
    saveCustomCardTypes(updatedTypes);
    setIsCreateDialogOpen(false);

    // Navigate to the editor page
    try {
      navigate({
        to: "/custom/$id/edit",
        params: { id: cardType.id },
      });
    } catch (error) {
      console.error("Navigation error:", error);
      // Fallback: try direct URL navigation
      window.location.href = `/custom/${cardType.id}/edit`;
    }
  };

  const handleEdit = (cardType: CustomCardType) => {
    console.log("Edit button clicked for card type:", cardType.id);
    console.log("Navigating to:", `/custom/${cardType.id}/edit`);
    console.log("Card type:", cardType);

    // Try multiple navigation approaches
    try {
      // Method 1: Using params
      console.log("Trying navigation with params...");
      navigate({
        to: "/custom/$id/edit",
        params: { id: cardType.id },
      });
    } catch (error) {
      console.error("Navigation with params failed:", error);

      try {
        // Method 2: Direct path
        console.log("Trying direct path navigation...");
        navigate({ to: `/custom/${cardType.id}/edit` as any });
      } catch (error2) {
        console.error("Direct path navigation failed:", error2);

        // Method 3: Fallback to window.location
        console.log("Using window.location fallback...");
        window.location.href = `/custom/${cardType.id}/edit`;
      }
    }
  };

  const handleDuplicate = (cardType: CustomCardType) => {
    const duplicated: CustomCardType = {
      ...cardType,
      id: `custom_${Date.now()}`,
      name: `${cardType.name} (Copy)`,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };
    const updatedTypes = [...customCardTypes, duplicated];
    saveCustomCardTypes(updatedTypes);
  };

  const handleDelete = (cardTypeId: string) => {
    if (confirm("Are you sure you want to delete this custom card type?")) {
      const updatedTypes = customCardTypes.filter((ct) => ct.id !== cardTypeId);
      saveCustomCardTypes(updatedTypes);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Custom Card Types</h1>
          <p className="text-gray-600 mt-2">
            Create and manage your custom card types with configurable layouts
            and fields.
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleCreateNew} className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Create New Type
          </Button>
          {/* Debug button */}
          {customCardTypes.length > 0 && (
            <Button
              variant="outline"
              onClick={() => {
                const testId = customCardTypes[0].id;
                console.log("Test navigation to:", `/custom/${testId}/edit`);
                window.location.href = `/custom/${testId}/edit`;
              }}
            >
              Test Route
            </Button>
          )}
        </div>
      </div>

      {/* Card Types Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {customCardTypes.map((cardType) => (
          <Card key={cardType.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex justify-between items-start">
                <span className="truncate">{cardType.name}</span>
                <div className="flex gap-1 ml-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEdit(cardType)}
                    className="h-8 w-8"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDuplicate(cardType)}
                    className="h-8 w-8"
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(cardType.id)}
                    className="h-8 w-8 text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardTitle>
              {cardType.description && (
                <CardDescription>{cardType.description}</CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-gray-600">
                <div>
                  <strong>Dimensions:</strong> {cardType.dimensions.width}×
                  {cardType.dimensions.height}mm
                </div>
                <div>
                  <strong>Components:</strong> {cardType.components.length}
                </div>
              </div>

              {onCreateCard && (
                <Button
                  className="w-full mt-4"
                  onClick={() => onCreateCard(cardType.id)}
                >
                  Create Card
                </Button>
              )}
            </CardContent>
          </Card>
        ))}

        {customCardTypes.length === 0 && (
          <div className="col-span-full text-center py-12">
            <div className="text-gray-400 mb-4">
              <Plus className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium">No custom card types yet</h3>
              <p className="text-sm">
                Create your first custom card type to get started.
              </p>
            </div>
            <Button onClick={handleCreateNew} className="mt-4">
              Create Your First Type
            </Button>
          </div>
        )}
      </div>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Palette className="w-5 h-5" />
              Create New Card Type
            </DialogTitle>
            <DialogDescription>
              Set up the basic properties for your custom card type. You'll be
              able to design the layout in the next step.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Card Type Name *</Label>
              <Input
                id="name"
                value={newCardType.name}
                onChange={(e) =>
                  setNewCardType((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="e.g., Character Card, Item Card"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newCardType.description}
                onChange={(e) =>
                  setNewCardType((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Optional description for this card type"
                className="mt-1"
                rows={2}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="width">Width (mm)</Label>
                <Input
                  id="width"
                  type="number"
                  value={newCardType.width}
                  onChange={(e) =>
                    setNewCardType((prev) => ({
                      ...prev,
                      width: Number(e.target.value),
                    }))
                  }
                  min="10"
                  max="200"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="height">Height (mm)</Label>
                <Input
                  id="height"
                  type="number"
                  value={newCardType.height}
                  onChange={(e) =>
                    setNewCardType((prev) => ({
                      ...prev,
                      height: Number(e.target.value),
                    }))
                  }
                  min="10"
                  max="300"
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="bleed">Bleed (mm)</Label>
                <Input
                  id="bleed"
                  type="number"
                  value={newCardType.bleed}
                  onChange={(e) =>
                    setNewCardType((prev) => ({
                      ...prev,
                      bleed: Number(e.target.value),
                    }))
                  }
                  min="0"
                  max="10"
                  step="0.5"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="dpi">DPI</Label>
                <Select
                  value={newCardType.dpi.toString()}
                  onValueChange={(value) =>
                    setNewCardType((prev) => ({ ...prev, dpi: Number(value) }))
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="150">150 DPI</SelectItem>
                    <SelectItem value="300">300 DPI</SelectItem>
                    <SelectItem value="600">600 DPI</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsCreateDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateCardType}>Create & Design</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
