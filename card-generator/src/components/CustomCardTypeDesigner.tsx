import { useState, useEffect } from "react";
import { useParams } from "@tanstack/react-router";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Save, Eye } from "lucide-react";
import type { CustomCardType } from "../types/customCard";
import { FigmaLikeEditor } from "./FigmaLikeEditor";

export function CustomCardTypeDesigner() {
  // Try to get ID from different possible routes
  let id: string | undefined;

  try {
    // Try the nested route first
    const nestedParams = useParams({ from: "/custom/$id/edit" });
    id = nestedParams.id;
  } catch (error) {
    console.log("Failed to get params from nested route:", error);
  }

  if (!id) {
    try {
      // Try the alternative route
      const altParams = useParams({ from: "/edit-custom/$id" });
      id = altParams.id;
    } catch (error) {
      console.log("Failed to get params from alt route:", error);
    }
  }

  // If still no ID, try to extract from URL manually
  if (!id) {
    const urlParts = window.location.pathname.split("/");
    if (urlParts.includes("custom") && urlParts.includes("edit")) {
      const editIndex = urlParts.indexOf("edit");
      if (editIndex > 0) {
        id = urlParts[editIndex - 1];
      }
    } else if (urlParts.includes("edit-custom")) {
      const editIndex = urlParts.indexOf("edit-custom");
      if (editIndex >= 0 && urlParts[editIndex + 1]) {
        id = urlParts[editIndex + 1];
      }
    }
  }

  const [cardType, setCardType] = useState<CustomCardType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("properties");

  // Debug logging
  console.log("CustomCardTypeDesigner loaded");
  console.log("ID from params:", id);
  console.log("Current URL:", window.location.href);
  console.log("URL pathname:", window.location.pathname);

  if (!id) {
    console.error("No ID parameter found");
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2 text-red-600">
            Invalid Route
          </h2>
          <p className="text-gray-600 mb-4">
            No card type ID provided in the URL.
          </p>
          <button
            onClick={() => (window.location.href = "/custom")}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Back to Card Types
          </button>
        </div>
      </div>
    );
  }

  // Load the card type from localStorage
  useEffect(() => {
    const stored = localStorage.getItem("customCardTypes");
    if (stored) {
      try {
        const cardTypes: CustomCardType[] = JSON.parse(stored);
        const foundCardType = cardTypes.find((ct) => ct.id === id);
        if (foundCardType) {
          setCardType(foundCardType);
        } else {
          // Card type not found, redirect back
          console.log("Card type not found, redirecting to /custom");
          window.location.href = "/custom";
          return;
        }
      } catch (error) {
        console.error("Failed to load custom card types:", error);
        window.location.href = "/custom";
        return;
      }
    } else {
      console.log("No stored card types, redirecting to /custom");
      window.location.href = "/custom";
      return;
    }
    setIsLoading(false);
  }, [id]);

  const saveCardType = (updatedCardType: CustomCardType) => {
    const stored = localStorage.getItem("customCardTypes");
    if (stored) {
      try {
        const cardTypes: CustomCardType[] = JSON.parse(stored);
        const updatedCardTypes = cardTypes.map((ct) =>
          ct.id === updatedCardType.id
            ? { ...updatedCardType, updatedAt: Date.now() }
            : ct
        );
        localStorage.setItem(
          "customCardTypes",
          JSON.stringify(updatedCardTypes)
        );
        setCardType({ ...updatedCardType, updatedAt: Date.now() });
      } catch (error) {
        console.error("Failed to save card type:", error);
      }
    }
  };

  const handleSave = () => {
    if (cardType) {
      saveCardType(cardType);
      // Show success message or toast
      alert("Card type saved successfully!");
    }
  };

  const handleBack = () => {
    window.location.href = "/custom";
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading card type...</p>
        </div>
      </div>
    );
  }

  if (!cardType) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Card Type Not Found</h2>
          <p className="text-gray-600 mb-4">
            The requested card type could not be found.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Card Types
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={handleBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{cardType.name}</h1>
              <p className="text-gray-600">
                {cardType.dimensions.width}×{cardType.dimensions.height}mm
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setActiveTab("preview")}>
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex-1 flex flex-col"
        >
          <TabsList className="grid w-full grid-cols-3 mx-6 mt-4">
            <TabsTrigger value="properties">Properties</TabsTrigger>
            <TabsTrigger value="design">Visual Design</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="properties" className="flex-1 p-6">
            <PropertiesEditor cardType={cardType} onChange={setCardType} />
          </TabsContent>

          <TabsContent value="design" className="flex-1 p-0">
            <FigmaLikeEditor cardType={cardType} onChange={setCardType} />
          </TabsContent>

          <TabsContent value="preview" className="flex-1 p-6">
            <PreviewPanel cardType={cardType} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

interface PropertiesEditorProps {
  cardType: CustomCardType;
  onChange: (cardType: CustomCardType) => void;
}

function PropertiesEditor({ cardType, onChange }: PropertiesEditorProps) {
  const updateCardType = (updates: Partial<CustomCardType>) => {
    const updated = { ...cardType, ...updates };
    onChange(updated);
  };

  const updateDimensions = (
    field: keyof CustomCardType["dimensions"],
    value: number
  ) => {
    updateCardType({
      dimensions: { ...cardType.dimensions, [field]: value },
    });
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Basic Properties */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Properties</CardTitle>
          <CardDescription>
            Configure the basic properties of your custom card type.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="name">Card Type Name</Label>
            <Input
              id="name"
              value={cardType.name}
              onChange={(e) => updateCardType({ name: e.target.value })}
              placeholder="Enter card type name"
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={cardType.description || ""}
              onChange={(e) => updateCardType({ description: e.target.value })}
              placeholder="Optional description for this card type"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Dimensions */}
      <Card>
        <CardHeader>
          <CardTitle>Card Dimensions</CardTitle>
          <CardDescription>
            Set the physical dimensions and print properties.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="width">Width (mm)</Label>
              <Input
                id="width"
                type="number"
                value={cardType.dimensions.width}
                onChange={(e) =>
                  updateDimensions("width", Number(e.target.value))
                }
                min="10"
                max="200"
              />
            </div>
            <div>
              <Label htmlFor="height">Height (mm)</Label>
              <Input
                id="height"
                type="number"
                value={cardType.dimensions.height}
                onChange={(e) =>
                  updateDimensions("height", Number(e.target.value))
                }
                min="10"
                max="300"
              />
            </div>
            <div>
              <Label htmlFor="bleed">Bleed (mm)</Label>
              <Input
                id="bleed"
                type="number"
                value={cardType.dimensions.bleed}
                onChange={(e) =>
                  updateDimensions("bleed", Number(e.target.value))
                }
                min="0"
                max="10"
                step="0.5"
              />
            </div>
            <div>
              <Label htmlFor="dpi">DPI</Label>
              <Select
                value={cardType.dimensions.dpi.toString()}
                onValueChange={(value) =>
                  updateDimensions("dpi", Number(value))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="150">150 DPI</SelectItem>
                  <SelectItem value="300">300 DPI</SelectItem>
                  <SelectItem value="600">600 DPI</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Component Information */}
      <Card>
        <CardHeader>
          <CardTitle>Components</CardTitle>
          <CardDescription>
            Use the Visual Design tab to add and configure text and image
            components.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <p>Components: {cardType.components.length}</p>
            <p className="text-sm mt-2">
              Switch to the Visual Design tab to add and edit components.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface PreviewPanelProps {
  cardType: CustomCardType;
}

function PreviewPanel({ cardType }: PreviewPanelProps) {
  return (
    <div className="h-full">
      <div className="text-center py-12">
        <Eye className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium mb-2">Preview Panel</h3>
        <p className="text-gray-600 mb-4">
          Preview your card design with sample data.
        </p>
        <div className="bg-gray-100 rounded-lg p-8 max-w-md mx-auto">
          <div
            className="bg-white rounded-lg shadow-md p-6"
            style={{
              width: `${cardType.dimensions.width * 2}px`,
              height: `${cardType.dimensions.height * 2}px`,
              maxWidth: "300px",
              maxHeight: "400px",
            }}
          >
            <div className="text-center">
              <h4 className="font-bold text-lg mb-2">{cardType.name}</h4>
              <p className="text-sm text-gray-600 mb-4">
                {cardType.description}
              </p>
              <div className="space-y-2">
                {cardType.components.map((component) => (
                  <div key={component.id} className="text-left">
                    <span className="font-medium text-sm">
                      {component.name}:
                    </span>
                    <span className="text-sm text-gray-600 ml-2">
                      {component.type === "text"
                        ? component.dataBinding.value
                        : "📷 Image"}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
